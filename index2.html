<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NCast - Player</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ffffff;
            color: #1f1f1f;
        }

        .container {
            width: 428px;
            height: 926px;
            margin: 0 auto;
            background-color: #ffffff;
            position: relative;
            overflow: hidden;
        }

        /* 头部区域 */
        .header {
            position: absolute;
            top: 58px;
            left: 32px;
            right: 32px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .back-btn {
            width: 48px;
            height: 48px;
            background-color: rgba(31, 31, 31, 0.1);
            border-radius: 24px;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
        }

        .back-arrow-group {
            position: relative;
            width: 16px;
            height: 14px;
        }

        .arrow-line {
            position: absolute;
            top: 7px;
            left: 0;
            width: 16px;
            height: 1px;
        }

        .arrow-path {
            position: absolute;
            top: 0;
            left: 0;
            width: 7px;
            height: 14px;
        }

        .now-playing {
            font-size: 20px;
            font-weight: 500;
            color: #1f1f1f;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .menu-btn {
            width: 24px;
            height: 24px;
            opacity: 0.5;
            cursor: pointer;
        }

        /* 专辑封面区域 */
        .album-cover-container {
            position: absolute;
            top: 138px;
            left: 32px;
            width: 364px;
            height: 364px;
        }

        .album-cover {
            width: 364px;
            height: 364px;
            border-radius: 24px;
            object-fit: cover;
        }

        /* 操作按钮区域 */
        .action-buttons {
            position: absolute;
            top: 466px;
            left: 49px;
            width: 266px;
            height: 72px;
            background-color: rgba(76, 0, 153, 0.1);
            border-radius: 48px;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 0 20px;
        }

        .action-btn {
            width: 32px;
            height: 32px;
            opacity: 0.5;
            cursor: pointer;
        }

        /* 歌曲信息 */
        .song-info {
            position: absolute;
            top: 570px;
            left: 32px;
            right: 32px;
            text-align: center;
        }

        .song-title {
            font-size: 20px;
            font-weight: 500;
            color: #1f1f1f;
            margin-bottom: 10px;
        }

        .song-category {
            font-size: 14px;
            color: rgba(31, 31, 31, 0.7);
        }

        /* 进度条区域 */
        .progress-container {
            position: absolute;
            top: 667px;
            left: 32px;
            right: 32px;
            height: 103px;
        }

        .time-labels {
            display: flex;
            justify-content: space-between;
            margin-bottom: 23px;
        }

        .time-label {
            font-size: 16px;
            color: rgba(31, 31, 31, 0.7);
        }

        .progress-bar {
            width: 100%;
            height: 60px;
            position: relative;
            display: flex;
            align-items: center;
            gap: 2px;
        }

        .bar {
            width: 5.74px;
            background-color: #4c0099;
            border-radius: 2px;
        }

        .bar.inactive {
            background-color: rgba(31, 31, 31, 0.2);
        }

        /* 控制按钮区域 */
        .controls {
            position: absolute;
            top: 810px;
            left: 32px;
            right: 32px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .control-btn {
            width: 32px;
            height: 32px;
            opacity: 0.7;
            cursor: pointer;
        }

        .play-btn {
            width: 80px;
            height: 80px;
            background-color: #4c0099;
            border-radius: 40px;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .play-icon {
            width: 32px;
            height: 32px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <button class="back-btn">
                <div class="back-arrow-group">
                    <img src="icons/arrow-line.png" alt="Arrow Line" class="arrow-line">
                    <img src="icons/arrow-path.png" alt="Arrow Path" class="arrow-path">
                </div>
            </button>
            <div class="now-playing">Now Playing</div>
            <img src="icons/dots-three.png" alt="Menu" class="menu-btn">
        </div>

        <!-- 专辑封面 -->
        <div class="album-cover-container">
            <img src="icons/album-cover.png" alt="Album Cover" class="album-cover">
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
            <img src="icons/share.png" alt="Share" class="action-btn">
            <img src="icons/heart.png" alt="Like" class="action-btn">
            <img src="icons/archive.png" alt="Archive" class="action-btn">
        </div>

        <!-- 歌曲信息 -->
        <div class="song-info">
            <div class="song-title">Sunday Vibes - Rift</div>
            <div class="song-category">Entertainment</div>
        </div>

        <!-- 进度条 -->
        <div class="progress-container">
            <div class="time-labels">
                <span class="time-label">07:00</span>
                <span class="time-label">15:00</span>
            </div>
            <div class="progress-bar">
                <!-- 进度条由多个小条组成，基于Figma数据的精确高度 -->
                <div class="bar" style="height: 20px;"></div>
                <div class="bar" style="height: 16px;"></div>
                <div class="bar" style="height: 30px;"></div>
                <div class="bar" style="height: 20px;"></div>
                <div class="bar" style="height: 16px;"></div>
                <div class="bar" style="height: 30px;"></div>
                <div class="bar" style="height: 30px;"></div>
                <div class="bar" style="height: 20px;"></div>
                <div class="bar" style="height: 30px;"></div>
                <div class="bar" style="height: 60px;"></div>
                <div class="bar" style="height: 30px;"></div>
                <div class="bar" style="height: 30px;"></div>
                <div class="bar" style="height: 30px;"></div>
                <div class="bar" style="height: 60px;"></div>
                <div class="bar" style="height: 46px;"></div>
                <div class="bar inactive" style="height: 26px;"></div>
                <div class="bar inactive" style="height: 16px;"></div>
                <div class="bar inactive" style="height: 20px;"></div>
                <div class="bar inactive" style="height: 16px;"></div>
                <div class="bar inactive" style="height: 30px;"></div>
                <div class="bar inactive" style="height: 30px;"></div>
                <div class="bar inactive" style="height: 30px;"></div>
                <div class="bar inactive" style="height: 60px;"></div>
                <div class="bar inactive" style="height: 30px;"></div>
                <div class="bar inactive" style="height: 30px;"></div>
                <div class="bar inactive" style="height: 30px;"></div>
                <div class="bar inactive" style="height: 60px;"></div>
                <div class="bar inactive" style="height: 26px;"></div>
                <div class="bar inactive" style="height: 16px;"></div>
            </div>
        </div>

        <!-- 控制按钮 -->
        <div class="controls">
            <img src="icons/arrows-up-down.png" alt="Shuffle" class="control-btn" style="position: absolute; left: 14px;">
            <img src="icons/clock-counter.png" alt="Previous" class="control-btn" style="position: absolute; left: 78px;">
            <button class="play-btn" style="position: absolute; left: 142px;">
                <img src="icons/play-button.png" alt="Play" class="play-icon">
            </button>
            <img src="icons/clock-clockwise.png" alt="Next" class="control-btn" style="position: absolute; left: 254px;">
            <img src="icons/sort-ascending.png" alt="Repeat" class="control-btn" style="position: absolute; left: 318px;">
        </div>
    </div>
</body>
</html>

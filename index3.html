<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NCast Player</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Public Sans', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #FFFFFF;
            color: #1F1F1F;
            width: 428px;
            height: 926px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
        }

        .player-container {
            width: 100%;
            height: 100%;
            position: relative;
        }

        /* Header */
        .header {
            position: absolute;
            top: 48px;
            left: 32px;
            right: 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 10;
        }

        .back-button {
            width: 48px;
            height: 48px;
            opacity: 0.1;
        }

        .menu-button {
            width: 24px;
            height: 24px;
            opacity: 0.5;
        }

        .now-playing {
            position: absolute;
            top: 58px;
            left: 104px;
            font-weight: 700;
            font-size: 24px;
            line-height: 28px;
            color: #1F1F1F;
        }

        /* Album Cover */
        .album-cover-container {
            position: absolute;
            top: 128px;
            left: 32px;
            width: 364px;
            height: 364px;
            border-radius: 24px;
            overflow: hidden;
            background: #D9D9D9;
        }

        .album-cover {
            width: 371.66px;
            height: 371.66px;
            margin-left: -3.83px;
            margin-top: -3.83px;
            object-fit: cover;
        }

        /* Action Buttons */
        .action-buttons {
            position: absolute;
            top: 456px;
            left: 81px;
            width: 266px;
            height: 72px;
            background: rgba(76, 0, 153, 0.1);
            backdrop-filter: blur(32px);
            border-radius: 48px;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 20px;
        }

        .action-button {
            width: 32px;
            height: 32px;
            opacity: 0.5;
        }

        /* Song Info */
        .song-title {
            position: absolute;
            top: 560px;
            left: 107px;
            width: 215px;
            font-weight: 700;
            font-size: 24px;
            line-height: 28px;
            text-align: center;
            color: #1F1F1F;
        }

        .song-category {
            position: absolute;
            top: 598px;
            left: 161px;
            width: 106px;
            font-weight: 400;
            font-size: 16px;
            line-height: 19px;
            text-align: center;
            color: #1F1F1F;
            opacity: 0.7;
        }

        /* Progress Section */
        .progress-section {
            position: absolute;
            top: 657px;
            left: 32px;
            width: 364px;
            height: 103px;
        }

        .time-labels {
            display: flex;
            justify-content: space-between;
            margin-bottom: 24px;
        }

        .time-label {
            font-weight: 500;
            font-size: 16px;
            line-height: 19px;
            color: #1F1F1F;
            opacity: 0.7;
        }

        .progress-bar {
            width: 100%;
            height: 60px;
            position: relative;
            display: flex;
            align-items: center;
            gap: 5.74px;
        }

        .progress-segment {
            width: 5.74px;
            background: #4C0099;
        }

        .progress-segment.inactive {
            background: #1F1F1F;
            opacity: 0.2;
        }

        /* Play Button */
        .play-button-container {
            position: absolute;
            top: 800px;
            left: 174px;
            width: 80px;
            height: 80px;
        }

        .play-button {
            width: 100%;
            height: 100%;
            background: #4C0099;
            border-radius: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
        }

        .play-icon {
            width: 32px;
            height: 32px;
            margin-left: 4px;
        }

        /* Control Icons */
        .control-icons {
            position: absolute;
            top: 824px;
            left: 46px;
            right: 46px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .control-icon {
            width: 32px;
            height: 32px;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="player-container">
        <!-- Header -->
        <div class="header">
            <img src="images/arrow-left.png" alt="Back" class="back-button">
            <img src="images/dots-menu.png" alt="Menu" class="menu-button">
        </div>

        <div class="now-playing">Now Playing</div>

        <!-- Album Cover -->
        <div class="album-cover-container">
            <img src="images/podcast-cover.png" alt="Podcast Cover" class="album-cover">
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <img src="images/share-icon.png" alt="Share" class="action-button">
            <img src="images/heart-icon.png" alt="Like" class="action-button">
            <img src="images/archive-icon.png" alt="Archive" class="action-button">
        </div>

        <!-- Song Info -->
        <div class="song-title">Sunday Vibes - Rift</div>
        <div class="song-category">Entertainment</div>

        <!-- Progress Section -->
        <div class="progress-section">
            <div class="time-labels">
                <span class="time-label">07:00</span>
                <span class="time-label">15:00</span>
            </div>
            <div class="progress-bar">
                <!-- Active segments -->
                <div class="progress-segment" style="height: 20px;"></div>
                <div class="progress-segment" style="height: 16px;"></div>
                <div class="progress-segment" style="height: 30px;"></div>
                <div class="progress-segment" style="height: 20px;"></div>
                <div class="progress-segment" style="height: 16px;"></div>
                <div class="progress-segment" style="height: 30px;"></div>
                <div class="progress-segment" style="height: 30px;"></div>
                <div class="progress-segment" style="height: 20px;"></div>
                <div class="progress-segment" style="height: 30px;"></div>
                <div class="progress-segment" style="height: 60px;"></div>
                <div class="progress-segment" style="height: 30px;"></div>
                <div class="progress-segment" style="height: 30px;"></div>
                <div class="progress-segment" style="height: 46px;"></div>
                <div class="progress-segment" style="height: 60px;"></div>

                <!-- Inactive segments -->
                <div class="progress-segment inactive" style="height: 26px;"></div>
                <div class="progress-segment inactive" style="height: 16px;"></div>
                <div class="progress-segment inactive" style="height: 20px;"></div>
                <div class="progress-segment inactive" style="height: 16px;"></div>
                <div class="progress-segment inactive" style="height: 30px;"></div>
                <div class="progress-segment inactive" style="height: 20px;"></div>
                <div class="progress-segment inactive" style="height: 16px;"></div>
                <div class="progress-segment inactive" style="height: 30px;"></div>
                <div class="progress-segment inactive" style="height: 30px;"></div>
                <div class="progress-segment inactive" style="height: 20px;"></div>
                <div class="progress-segment inactive" style="height: 30px;"></div>
                <div class="progress-segment inactive" style="height: 60px;"></div>
                <div class="progress-segment inactive" style="height: 30px;"></div>
                <div class="progress-segment inactive" style="height: 30px;"></div>
                <div class="progress-segment inactive" style="height: 60px;"></div>
            </div>
        </div>

        <!-- Play Button -->
        <div class="play-button-container">
            <button class="play-button">
                <svg class="play-icon" viewBox="0 0 32 32" fill="none">
                    <path d="M8 6L24 16L8 26V6Z" fill="white"/>
                </svg>
            </button>
        </div>

        <!-- Control Icons -->
        <div class="control-icons">
            <img src="images/speed-icon.png" alt="Speed" class="control-icon">
            <img src="images/rewind-icon.png" alt="Rewind" class="control-icon">
            <img src="images/forward-icon.png" alt="Forward" class="control-icon">
            <img src="images/sort-icon.png" alt="Sort" class="control-icon">
        </div>
    </div>
</body>
</html>
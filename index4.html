<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NCast - Player</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ffffff;
            width: 428px;
            height: 926px;
            overflow: hidden;
            position: relative;
        }

        .player-container {
            width: 100%;
            height: 100%;
            background: #ffffff;
            position: relative;
            padding: 0 32px;
        }

        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-top: 48px;
            margin-bottom: 32px;
        }

        .back-button {
            width: 48px;
            height: 48px;
            background: rgba(31, 31, 31, 0.1);
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
        }

        .back-button img {
            width: 16px;
            height: 14px;
        }

        .now-playing {
            font-size: 20px;
            font-weight: 600;
            color: #1f1f1f;
        }

        .menu-button {
            width: 24px;
            height: 24px;
            opacity: 0.5;
            cursor: pointer;
        }

        .album-cover {
            width: 364px;
            height: 364px;
            border-radius: 24px;
            margin: 0 auto 32px;
            overflow: hidden;
        }

        .album-cover img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .action-buttons {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 38px;
            margin-bottom: 32px;
            padding: 20px 0;
            background: rgba(76, 0, 153, 0.1);
            border-radius: 48px;
            width: 266px;
            margin-left: auto;
            margin-right: auto;
        }

        .action-button {
            width: 32px;
            height: 32px;
            opacity: 0.5;
            cursor: pointer;
        }

        .song-info {
            text-align: center;
            margin-bottom: 40px;
        }

        .song-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f1f1f;
            margin-bottom: 8px;
        }

        .song-category {
            font-size: 16px;
            color: #1f1f1f;
            opacity: 0.7;
        }

        .progress-section {
            margin-bottom: 40px;
        }

        .time-labels {
            display: flex;
            justify-content: space-between;
            margin-bottom: 24px;
            font-size: 16px;
            color: #1f1f1f;
            opacity: 0.7;
        }

        .progress-bar {
            width: 100%;
            height: 60px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .controls {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
        }

        .control-button {
            width: 32px;
            height: 32px;
            opacity: 0.7;
            cursor: pointer;
        }

        .play-button {
            width: 80px;
            height: 80px;
            background: #4c0099;
            border-radius: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border: none;
        }

        .play-button img {
            width: 32px;
            height: 32px;
        }
    </style>
</head>
<body>
    <div class="player-container">
        <div class="header">
            <button class="back-button">
                <img src="arrow-circle-left-fill.png" alt="Back">
            </button>
            <div class="now-playing">Now Playing</div>
            <img src="dots-three-outline-vertical-fill.png" alt="Menu" class="menu-button">
        </div>

        <div class="album-cover">
            <img src="podcast-cover.png" alt="Album Cover">
        </div>

        <div class="action-buttons">
            <img src="share-network-fill.png" alt="Share" class="action-button">
            <img src="heart-fill.png" alt="Like" class="action-button">
            <img src="archive-box-fill.png" alt="Archive" class="action-button">
        </div>

        <div class="song-info">
            <div class="song-title">Sunday Vibes - Rift</div>
            <div class="song-category">Entertainment</div>
        </div>

        <div class="progress-section">
            <div class="time-labels">
                <span>07:00</span>
                <span>15:00</span>
            </div>
            <div class="progress-bar">
                <img src="group-6.png" alt="Progress Bar" style="width: 100%; height: 60px; object-fit: contain;">
            </div>
        </div>

        <div class="controls">
            <img src="arrows-down-up-fill.png" alt="Shuffle" class="control-button">
            <img src="clock-counter-clockwise-fill.png" alt="Rewind" class="control-button">
            <button class="play-button">
                <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                    <path d="M8 6L24 16L8 26V6Z" fill="white"/>
                </svg>
            </button>
            <img src="clock-clockwise-fill.png" alt="Forward" class="control-button">
            <img src="sort-ascending-fill.png" alt="Sort" class="control-button">
        </div>
    </div>
</body>
</html>
